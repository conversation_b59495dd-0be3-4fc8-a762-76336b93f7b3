"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@react-email";
exports.ids = ["vendor-chunks/@react-email"];
exports.modules = {

/***/ "(rsc)/./node_modules/@react-email/render/dist/node/index.mjs":
/*!**************************************************************!*\
  !*** ./node_modules/@react-email/render/dist/node/index.mjs ***!
  \**************************************************************/
/***/ ((__webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(__webpack_module__, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   plainTextSelectors: () => (/* binding */ plainTextSelectors),\n/* harmony export */   pretty: () => (/* binding */ pretty),\n/* harmony export */   render: () => (/* binding */ render),\n/* harmony export */   renderAsync: () => (/* binding */ renderAsync)\n/* harmony export */ });\n/* harmony import */ var html_to_text__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! html-to-text */ \"(rsc)/./node_modules/html-to-text/lib/html-to-text.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var prettier_plugins_html__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! prettier/plugins/html */ \"prettier/plugins/html\");\n/* harmony import */ var prettier_standalone__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! prettier/standalone */ \"prettier/standalone\");\n/* harmony import */ var node_stream__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! node:stream */ \"node:stream\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react/jsx-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-runtime.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([prettier_plugins_html__WEBPACK_IMPORTED_MODULE_2__, prettier_standalone__WEBPACK_IMPORTED_MODULE_3__]);\n([prettier_plugins_html__WEBPACK_IMPORTED_MODULE_2__, prettier_standalone__WEBPACK_IMPORTED_MODULE_3__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\nvar __defProp = Object.defineProperty;\nvar __defProps = Object.defineProperties;\nvar __getOwnPropDescs = Object.getOwnPropertyDescriptors;\nvar __getOwnPropSymbols = Object.getOwnPropertySymbols;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __propIsEnum = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp.call(b, prop))\n      __defNormalProp(a, prop, b[prop]);\n  if (__getOwnPropSymbols)\n    for (var prop of __getOwnPropSymbols(b)) {\n      if (__propIsEnum.call(b, prop))\n        __defNormalProp(a, prop, b[prop]);\n    }\n  return a;\n};\nvar __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));\nvar __async = (__this, __arguments, generator) => {\n  return new Promise((resolve, reject) => {\n    var fulfilled = (value) => {\n      try {\n        step(generator.next(value));\n      } catch (e) {\n        reject(e);\n      }\n    };\n    var rejected = (value) => {\n      try {\n        step(generator.throw(value));\n      } catch (e) {\n        reject(e);\n      }\n    };\n    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);\n    step((generator = generator.apply(__this, __arguments)).next());\n  });\n};\n\n// src/node/render.tsx\n\n\n\n// src/shared/plain-text-selectors.ts\nvar plainTextSelectors = [\n  { selector: \"img\", format: \"skip\" },\n  { selector: \"[data-skip-in-text=true]\", format: \"skip\" },\n  {\n    selector: \"a\",\n    options: { linkBrackets: false }\n  }\n];\n\n// src/shared/utils/pretty.ts\n\n\nfunction recursivelyMapDoc(doc, callback) {\n  if (Array.isArray(doc)) {\n    return doc.map((innerDoc) => recursivelyMapDoc(innerDoc, callback));\n  }\n  if (typeof doc === \"object\") {\n    if (doc.type === \"group\") {\n      return __spreadProps(__spreadValues({}, doc), {\n        contents: recursivelyMapDoc(doc.contents, callback),\n        expandedStates: recursivelyMapDoc(\n          doc.expandedStates,\n          callback\n        )\n      });\n    }\n    if (\"contents\" in doc) {\n      return __spreadProps(__spreadValues({}, doc), {\n        contents: recursivelyMapDoc(doc.contents, callback)\n      });\n    }\n    if (\"parts\" in doc) {\n      return __spreadProps(__spreadValues({}, doc), {\n        parts: recursivelyMapDoc(doc.parts, callback)\n      });\n    }\n    if (doc.type === \"if-break\") {\n      return __spreadProps(__spreadValues({}, doc), {\n        breakContents: recursivelyMapDoc(doc.breakContents, callback),\n        flatContents: recursivelyMapDoc(doc.flatContents, callback)\n      });\n    }\n  }\n  return callback(doc);\n}\nvar modifiedHtml = __spreadValues({}, prettier_plugins_html__WEBPACK_IMPORTED_MODULE_2__);\nif (modifiedHtml.printers) {\n  const previousPrint = modifiedHtml.printers.html.print;\n  modifiedHtml.printers.html.print = (path, options, print, args) => {\n    const node = path.getNode();\n    const rawPrintingResult = previousPrint(path, options, print, args);\n    if (node.type === \"ieConditionalComment\") {\n      const printingResult = recursivelyMapDoc(rawPrintingResult, (doc) => {\n        if (typeof doc === \"object\" && doc.type === \"line\") {\n          return doc.soft ? \"\" : \" \";\n        }\n        return doc;\n      });\n      return printingResult;\n    }\n    return rawPrintingResult;\n  };\n}\nvar defaults = {\n  endOfLine: \"lf\",\n  tabWidth: 2,\n  plugins: [modifiedHtml],\n  bracketSameLine: true,\n  parser: \"html\"\n};\nvar pretty = (str, options = {}) => {\n  return (0,prettier_standalone__WEBPACK_IMPORTED_MODULE_3__.format)(str.replaceAll(\"\\0\", \"\"), __spreadValues(__spreadValues({}, defaults), options));\n};\n\n// src/node/read-stream.ts\n\nvar decoder = new TextDecoder(\"utf-8\");\nvar readStream = (stream) => __async(void 0, null, function* () {\n  let result = \"\";\n  if (\"pipeTo\" in stream) {\n    const writableStream = new WritableStream({\n      write(chunk) {\n        result += decoder.decode(chunk);\n      }\n    });\n    yield stream.pipeTo(writableStream);\n  } else {\n    const writable = new node_stream__WEBPACK_IMPORTED_MODULE_4__.Writable({\n      write(chunk, _encoding, callback) {\n        result += decoder.decode(chunk);\n        callback();\n      }\n    });\n    stream.pipe(writable);\n    yield new Promise((resolve, reject) => {\n      writable.on(\"error\", reject);\n      writable.on(\"close\", () => {\n        resolve();\n      });\n    });\n  }\n  return result;\n});\n\n// src/node/render.tsx\n\nvar render = (node, options) => __async(void 0, null, function* () {\n  const suspendedElement = /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, { children: node });\n  const reactDOMServer = yield Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/next\"), __webpack_require__.e(1)]).then(__webpack_require__.t.bind(__webpack_require__, /*! react-dom/server */ \"(rsc)/./node_modules/next/dist/compiled/react-dom/server.js\", 19)).then(\n    // This is beacuse react-dom/server is CJS\n    (m) => m.default\n  );\n  let html2;\n  if (Object.hasOwn(reactDOMServer, \"renderToReadableStream\")) {\n    html2 = yield readStream(\n      yield reactDOMServer.renderToReadableStream(suspendedElement)\n    );\n  } else {\n    yield new Promise((resolve, reject) => {\n      const stream = reactDOMServer.renderToPipeableStream(suspendedElement, {\n        onAllReady() {\n          return __async(this, null, function* () {\n            html2 = yield readStream(stream);\n            resolve();\n          });\n        },\n        onError(error) {\n          reject(error);\n        }\n      });\n    });\n  }\n  if (options == null ? void 0 : options.plainText) {\n    return (0,html_to_text__WEBPACK_IMPORTED_MODULE_0__.convert)(html2, __spreadValues({\n      selectors: plainTextSelectors\n    }, options.htmlToTextOptions));\n  }\n  const doctype = '<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Transitional//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd\">';\n  const document = `${doctype}${html2.replace(/<!DOCTYPE.*?>/, \"\")}`;\n  if (options == null ? void 0 : options.pretty) {\n    return pretty(document);\n  }\n  return document;\n});\n\n// src/node/index.ts\nvar renderAsync = (element, options) => {\n  return render(element, options);\n};\n\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@react-email/render/dist/node/index.mjs\n");

/***/ })

};
;