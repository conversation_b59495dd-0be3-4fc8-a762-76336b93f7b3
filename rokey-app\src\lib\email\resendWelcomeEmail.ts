import { Resend } from 'resend';

// Initialize Resend client lazily to ensure environment variables are available
function getResendClient(): Resend {
  const apiKey = process.env.RESEND_API_KEY;

  if (!apiKey) {
    throw new Error('RESEND_API_KEY environment variable is not set');
  }

  return new Resend(apiKey);
}

interface WelcomeEmailData {
  userEmail: string;
  userName: string;
  userTier: string;
}

/**
 * Send welcome email using Resend (server-side optimized)
 */
export async function sendResendWelcomeEmail(data: WelcomeEmailData): Promise<boolean> {
  try {
    console.log('🔍 RESEND: Starting email send process...');

    // Check environment variable availability
    const apiKey = process.env.RESEND_API_KEY;
    if (!apiKey) {
      console.error('❌ RESEND: RESEND_API_KEY environment variable is not set');
      return false;
    }

    const { userEmail, userName, userTier } = data;
    
    // Email content - Matching <PERSON><PERSON><PERSON><PERSON>'s dark theme with orange accents
    const htmlContent = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome to RouKey</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            color: #e5e7eb;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #040716 0%, #1C051C 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        .header {
            background: linear-gradient(135deg, #ea580c 0%, #f97316 50%, #fb923c 100%);
            color: white;
            padding: 50px 40px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.3;
        }
        .header h1 {
            margin: 0;
            font-size: 32px;
            font-weight: 700;
            position: relative;
            z-index: 1;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }
        .header p {
            margin: 12px 0 0 0;
            font-size: 18px;
            opacity: 0.95;
            position: relative;
            z-index: 1;
        }
        .content {
            padding: 40px;
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
        }
        .highlight {
            background: linear-gradient(135deg, rgba(251, 146, 60, 0.15) 0%, rgba(249, 115, 22, 0.15) 100%);
            padding: 24px;
            border-radius: 12px;
            margin: 24px 0;
            border: 1px solid rgba(251, 146, 60, 0.3);
            position: relative;
        }
        .highlight::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 4px;
            background: linear-gradient(135deg, #ea580c 0%, #f97316 100%);
            border-radius: 2px;
        }
        .feature {
            background: rgba(15, 23, 42, 0.8);
            padding: 24px;
            margin: 16px 0;
            border-radius: 12px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            position: relative;
        }
        .feature::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 3px;
            background: linear-gradient(135deg, #ea580c 0%, #f97316 100%);
            border-radius: 2px;
        }
        .feature strong {
            color: #fb923c;
            display: block;
            margin-bottom: 8px;
            font-size: 16px;
        }
        .feature-text {
            color: #f1f5f9 !important;
        }
        .button {
            display: inline-block;
            background: linear-gradient(135deg, #ea580c 0%, #f97316 100%);
            color: #ffffff !important;
            padding: 16px 32px;
            text-decoration: none;
            border-radius: 10px;
            font-weight: 600;
            margin: 12px 12px 12px 0;
            box-shadow: 0 4px 14px 0 rgba(249, 115, 22, 0.3);
            border: 1px solid rgba(251, 146, 60, 0.2);
        }
        .button-secondary {
            background: linear-gradient(135deg, #374151 0%, #4b5563 100%);
            box-shadow: 0 4px 14px 0 rgba(75, 85, 99, 0.3);
            border: 1px solid rgba(156, 163, 175, 0.2);
            color: #ffffff !important;
        }
        .footer {
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
            padding: 40px;
            text-align: center;
            font-size: 14px;
            color: #9ca3af;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }
        .footer strong {
            color: #fb923c;
        }
        .footer a {
            color: #fb923c;
            text-decoration: none;
        }
        ul, ol {
            padding-left: 20px;
            color: #f1f5f9;
        }
        li {
            margin-bottom: 10px;
            color: #f1f5f9;
        }
        .text-center {
            text-align: center;
        }
        .tier-badge {
            display: inline-block;
            background: linear-gradient(135deg, #059669 0%, #10b981 100%);
            color: white;
            padding: 6px 16px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
        }
        h2 {
            color: #fb923c;
            margin-top: 32px;
            margin-bottom: 16px;
            font-size: 24px;
            font-weight: 600;
        }
        p {
            color: #f1f5f9;
            margin-bottom: 16px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Welcome to RouKey! 🚀</h1>
            <p>Your AI Gateway is Ready</p>
        </div>
        
        <div class="content">
            <p>Hi <strong>${userName}</strong>,</p>

            <p>Welcome to RouKey! We're excited to have you join our community of developers building smarter AI applications with intelligent routing and cost optimization.</p>

            <div class="highlight">
                <p><strong>⚡ Your <span class="tier-badge">${userTier}</span> account is now active and ready to use!</strong></p>
            </div>

            <h2>🚀 Quick Start Guide</h2>

            <div class="feature">
                <strong>1. Access Your Dashboard</strong>
                <span class="feature-text">Start configuring your AI routing strategies and monitoring performance in real-time.</span>
            </div>

            <div class="feature">
                <strong>2. Add Your API Keys</strong>
                <span class="feature-text">Connect your OpenAI, Anthropic, Google, and other provider keys for intelligent routing across 300+ models.</span>
            </div>

            <div class="feature">
                <strong>3. Configure Smart Routing</strong>
                <span class="feature-text">Choose from intelligent routing strategies: complexity-based, cost-optimized, role-based, and more.</span>
            </div>

            <div class="feature">
                <strong>4. Start Optimizing</strong>
                <span class="feature-text">Begin saving costs immediately with automatic model selection and fallback protection.</span>
            </div>

            <h2>✨ What You Can Do Next</h2>
            <ul>
                <li><strong>Intelligent Routing:</strong> Let RouKey automatically route requests to the optimal model based on complexity and cost</li>
                <li><strong>Multiple Providers:</strong> Add keys from 50+ AI providers for maximum reliability and performance</li>
                <li><strong>Real-time Analytics:</strong> Monitor costs, latency, success rates, and performance metrics</li>
                <li><strong>Advanced Features:</strong> Explore custom roles, semantic caching, and knowledge base integration</li>
            </ul>

            <h2>💡 Pro Tips for Success</h2>
            <ol>
                <li><strong>Start Simple:</strong> Begin with fallback routing, then explore advanced strategies</li>
                <li><strong>Diversify Providers:</strong> Add multiple API keys for better reliability and cost optimization</li>
                <li><strong>Monitor Performance:</strong> Use our analytics dashboard to track savings and optimize further</li>
            </ol>

            <div class="text-center" style="margin: 40px 0;">
                <a href="https://roukey.online/dashboard" class="button">Open Dashboard</a>
                <a href="https://roukey.online/docs" class="button button-secondary">View Documentation</a>
            </div>

            <p>Thank you for choosing RouKey. We're here to help you build better AI applications with smarter routing!</p>

            <p>Best regards,<br>
            <strong>The RouKey Team</strong></p>
        </div>
        
        <div class="footer">
            <p><strong>RouKey - Smart AI Gateway</strong></p>
            <p>© ${new Date().getFullYear()} DRIM LLC. All rights reserved.</p>
            <p>Need help? Reply to this email or contact us at <a href="mailto:<EMAIL>" style="color: #ff6b35;"><EMAIL></a></p>
            <p style="margin-top: 20px; font-size: 12px; color: #999;">
                You received this email because you signed up for RouKey. 
                If you have any questions, please contact our support team.
            </p>
        </div>
    </div>
</body>
</html>`;

    // Send email using Resend
    console.log('🔍 RESEND: Initializing Resend client...');
    let resend;
    try {
      resend = getResendClient();
    } catch (clientError) {
      console.error('❌ RESEND: Failed to initialize Resend client:', clientError);
      return false;
    }

    console.log('🔍 RESEND: Sending email...');
    const { data: emailData, error } = await resend.emails.send({
      from: 'RouKey <<EMAIL>>', // Use your verified domain
      to: [userEmail],
      subject: 'Welcome to RouKey - Your AI Gateway is Ready! 🚀',
      html: htmlContent,
      text: `Hi ${userName},

Welcome to RouKey! Your ${userTier} account is now active.

Quick Start:
1. Access Your Dashboard: https://roukey.online/dashboard
2. Add Your API Keys
3. Set Up Routing
4. Start Saving

Need help? Contact <NAME_EMAIL>

Best regards,
The RouKey Team

© ${new Date().getFullYear()} DRIM LLC. All rights reserved.`,
    });

    if (error) {
      console.error('❌ Resend error:', error);
      return false;
    }

    console.log('✅ Welcome email sent successfully via Resend to:', userEmail);
    console.log('📧 Email ID:', emailData?.id);
    return true;

  } catch (error) {
    console.error('❌ Failed to send welcome email via Resend:', error);
    return false;
  }
}
