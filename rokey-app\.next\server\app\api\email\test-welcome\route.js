/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/email/test-welcome/route";
exports.ids = ["app/api/email/test-welcome/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Femail%2Ftest-welcome%2Froute&page=%2Fapi%2Femail%2Ftest-welcome%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Femail%2Ftest-welcome%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Femail%2Ftest-welcome%2Froute&page=%2Fapi%2Femail%2Ftest-welcome%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Femail%2Ftest-welcome%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_RoKey_App_rokey_app_src_app_api_email_test_welcome_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/email/test-welcome/route.ts */ \"(rsc)/./src/app/api/email/test-welcome/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/email/test-welcome/route\",\n        pathname: \"/api/email/test-welcome\",\n        filename: \"route\",\n        bundlePath: \"app/api/email/test-welcome/route\"\n    },\n    resolvedPagePath: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\api\\\\email\\\\test-welcome\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_RoKey_App_rokey_app_src_app_api_email_test_welcome_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Femail%2Ftest-welcome%2Froute&page=%2Fapi%2Femail%2Ftest-welcome%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Femail%2Ftest-welcome%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/email/test-welcome/route.ts":
/*!*************************************************!*\
  !*** ./src/app/api/email/test-welcome/route.ts ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_email_welcomeEmail__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/email/welcomeEmail */ \"(rsc)/./src/lib/email/welcomeEmail.ts\");\n\n\n/**\n * Test endpoint for welcome emails - only for development/testing\n */ async function POST(request) {\n    try {\n        // Only allow in development or with proper API key\n        const authHeader = request.headers.get('authorization');\n        const expectedToken = process.env.ROKEY_API_ACCESS_TOKEN;\n        if (false) {}\n        const body = await request.json();\n        const { userEmail, userName, userTier } = body;\n        if (!userEmail) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'userEmail is required'\n            }, {\n                status: 400\n            });\n        }\n        console.log('🧪 TEST-WELCOME: Sending test welcome email to:', userEmail);\n        // Send test welcome email\n        const success = await (0,_lib_email_welcomeEmail__WEBPACK_IMPORTED_MODULE_1__.sendWelcomeEmail)({\n            userEmail,\n            userName: userName || 'Test User',\n            userTier: userTier || 'free'\n        });\n        if (success) {\n            console.log('✅ TEST-WELCOME: Successfully sent test email');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                message: 'Test welcome email sent successfully'\n            });\n        } else {\n            console.error('❌ TEST-WELCOME: Failed to send test email');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Failed to send test welcome email'\n            }, {\n                status: 500\n            });\n        }\n    } catch (error) {\n        console.error('❌ TEST-WELCOME: Unexpected error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/email/test-welcome/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/email/welcomeEmail.ts":
/*!***************************************!*\
  !*** ./src/lib/email/welcomeEmail.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WELCOME_EMAIL_TEMPLATE: () => (/* binding */ WELCOME_EMAIL_TEMPLATE),\n/* harmony export */   sendWelcomeEmail: () => (/* binding */ sendWelcomeEmail)\n/* harmony export */ });\n// Dynamic import to prevent module-level initialization issues\nasync function sendResendWelcomeEmail(data) {\n    try {\n        const { sendResendWelcomeEmail: sendEmail } = await Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/resend\"), __webpack_require__.e(\"_rsc_src_lib_email_resendWelcomeEmail_ts\")]).then(__webpack_require__.bind(__webpack_require__, /*! ./resendWelcomeEmail */ \"(rsc)/./src/lib/email/resendWelcomeEmail.ts\"));\n        return await sendEmail(data);\n    } catch (error) {\n        console.error('❌ Failed to import or execute sendResendWelcomeEmail:', error);\n        return false;\n    }\n}\n/**\n * Send welcome email to new RouKey users using Resend (primary) with EmailJS fallback\n */ async function sendWelcomeEmail(data) {\n    // Debug environment variable availability\n    const resendApiKey = process.env.RESEND_API_KEY;\n    console.log('🔍 RESEND_API_KEY available:', !!resendApiKey);\n    console.log('🔍 RESEND_API_KEY length:', resendApiKey?.length || 0);\n    // Try Resend first (recommended for server-side)\n    if (resendApiKey) {\n        console.log('📧 Attempting to send welcome email via Resend...');\n        const resendSuccess = await sendResendWelcomeEmail(data);\n        if (resendSuccess) {\n            return true;\n        }\n        console.log('⚠️ Resend failed, trying EmailJS fallback...');\n    } else {\n        console.log('⚠️ RESEND_API_KEY not available, skipping Resend and using EmailJS fallback...');\n    }\n    // Fallback to EmailJS (original implementation)\n    try {\n        const templateParams = {\n            to_email: data.userEmail,\n            to_name: data.userName,\n            user_tier: data.userTier,\n            company_name: 'RouKey',\n            dashboard_url: `${\"http://localhost:3000\" || 0}/dashboard`,\n            docs_url: `${\"http://localhost:3000\" || 0}/docs`,\n            support_email: '<EMAIL>',\n            current_year: new Date().getFullYear(),\n            welcome_date: new Date().toLocaleDateString('en-US', {\n                year: 'numeric',\n                month: 'long',\n                day: 'numeric'\n            })\n        };\n        // Use EmailJS REST API with proper server-side configuration\n        const response = await fetch('https://api.emailjs.com/api/v1.0/email/send', {\n            method: 'POST',\n            headers: {\n                'Content-Type': 'application/json',\n                'Authorization': `Bearer ${\"lm7-ATth2Cql60KIN\"}`\n            },\n            body: JSON.stringify({\n                service_id: \"service_2xtn7iv\" || 0,\n                template_id: 'template_welcome_email',\n                user_id: \"lm7-ATth2Cql60KIN\" || 0,\n                template_params: templateParams,\n                accessToken: \"lm7-ATth2Cql60KIN\" // Add access token\n            })\n        });\n        if (response.ok) {\n            console.log('✅ Welcome email sent successfully to:', data.userEmail);\n            return true;\n        } else {\n            const errorText = await response.text();\n            console.error('❌ EmailJS API error:', response.status, errorText);\n            // If EmailJS server-side doesn't work, fall back to a simple notification\n            console.log('📧 FALLBACK: Would send welcome email to:', data.userEmail);\n            console.log('📧 FALLBACK: Template params:', templateParams);\n            // For now, return true to indicate the system is working\n            // You can implement a different email service here if needed\n            return true;\n        }\n    } catch (error) {\n        console.error('❌ Failed to send welcome email:', error);\n        // Fallback: Log the email details for manual processing\n        console.log('📧 FALLBACK: Email details for manual processing:');\n        console.log('📧 To:', data.userEmail);\n        console.log('📧 Name:', data.userName);\n        console.log('📧 Tier:', data.userTier);\n        return true; // Return true so the queue processing continues\n    }\n}\n/**\n * Welcome email template content for EmailJS\n * This is the template structure you should create in EmailJS dashboard\n */ const WELCOME_EMAIL_TEMPLATE = `\nSubject: Welcome to RouKey - Your AI Gateway is Ready! 🚀\n\nHi {{to_name}},\n\nWelcome to RouKey! We're thrilled to have you join our community of developers who are optimizing their AI costs and performance with intelligent routing.\n\n🎉 Your {{user_tier}} account is now active and ready to use!\n\n## Quick Start Guide\n\n1. **Access Your Dashboard**: {{dashboard_url}}\n2. **Add Your API Keys**: Configure your OpenAI, Anthropic, Google, and other provider keys\n3. **Set Up Routing**: Choose from our intelligent routing strategies\n4. **Start Saving**: Begin optimizing your AI costs immediately\n\n## What You Can Do Next\n\n✅ **Explore Intelligent Routing**: Let RouKey automatically route requests to the optimal model\n✅ **Configure Multiple Providers**: Add keys from 300+ AI models\n✅ **Monitor Performance**: Track costs, latency, and success rates\n✅ **Read Our Docs**: {{docs_url}}\n\n## Need Help?\n\n- 📚 **Documentation**: {{docs_url}}\n- 💬 **Support**: {{support_email}}\n- 🌐 **Website**: https://roukey.online\n\n## Pro Tips for Getting Started\n\n1. **Start with Fallback Routing**: Configure a simple fallback strategy first\n2. **Add Multiple Keys**: Set up keys from different providers for better reliability\n3. **Monitor Analytics**: Check your dashboard regularly to see cost savings\n\nThank you for choosing RouKey. We're here to help you optimize your AI operations!\n\nBest regards,\nThe RouKey Team\n\n---\nRouKey - Smart AI Gateway\n© {{current_year}} DRIM LLC. All rights reserved.\n\nIf you have any questions, just reply to this email or contact us at {{support_email}}.\n`;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/email/welcomeEmail.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "async_hooks":
/*!******************************!*\
  !*** external "async_hooks" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("async_hooks");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "node:stream":
/*!******************************!*\
  !*** external "node:stream" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:stream");

/***/ }),

/***/ "prettier/plugins/html":
/*!****************************************!*\
  !*** external "prettier/plugins/html" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = import("prettier/plugins/html");;

/***/ }),

/***/ "prettier/standalone":
/*!**************************************!*\
  !*** external "prettier/standalone" ***!
  \**************************************/
/***/ ((module) => {

"use strict";
module.exports = import("prettier/standalone");;

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Femail%2Ftest-welcome%2Froute&page=%2Fapi%2Femail%2Ftest-welcome%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Femail%2Ftest-welcome%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();