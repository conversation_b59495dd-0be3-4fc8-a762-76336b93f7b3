"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_rsc_src_lib_email_resendWelcomeEmail_ts";
exports.ids = ["_rsc_src_lib_email_resendWelcomeEmail_ts"];
exports.modules = {

/***/ "(rsc)/./src/lib/email/resendWelcomeEmail.ts":
/*!*********************************************!*\
  !*** ./src/lib/email/resendWelcomeEmail.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   sendResendWelcomeEmail: () => (/* binding */ sendResendWelcomeEmail)\n/* harmony export */ });\n/* harmony import */ var resend__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! resend */ \"(rsc)/./node_modules/resend/dist/index.mjs\");\n\n// Initialize Resend client lazily to ensure environment variables are available\nfunction getResendClient() {\n    const apiKey = process.env.RESEND_API_KEY;\n    if (!apiKey) {\n        console.error('❌ RESEND: Environment variables available:', Object.keys(process.env).filter((key)=>key.includes('RESEND')));\n        console.error('❌ RESEND: NODE_ENV:', \"development\");\n        console.error('❌ RESEND: VERCEL_ENV:', process.env.VERCEL_ENV);\n        throw new Error('RESEND_API_KEY environment variable is not set');\n    }\n    console.log('✅ RESEND: Successfully found API key, length:', apiKey.length);\n    return new resend__WEBPACK_IMPORTED_MODULE_0__.Resend(apiKey);\n}\n/**\n * Send welcome email using Resend (server-side optimized)\n */ async function sendResendWelcomeEmail(data) {\n    try {\n        console.log('🔍 RESEND: Starting email send process...');\n        // Check environment variable availability\n        const apiKey = process.env.RESEND_API_KEY;\n        if (!apiKey) {\n            console.error('❌ RESEND: RESEND_API_KEY environment variable is not set');\n            return false;\n        }\n        const { userEmail, userName, userTier } = data;\n        // Email content - Matching RouKey's dark theme with orange accents\n        const htmlContent = `\n<!DOCTYPE html>\n<html>\n<head>\n    <meta charset=\"utf-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>Welcome to RouKey</title>\n    <style>\n        body {\n            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;\n            line-height: 1.6;\n            color: #e5e7eb;\n            margin: 0;\n            padding: 20px;\n            background: linear-gradient(135deg, #040716 0%, #1C051C 100%);\n            min-height: 100vh;\n        }\n        .container {\n            max-width: 600px;\n            margin: 0 auto;\n            background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);\n            border-radius: 16px;\n            overflow: hidden;\n            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);\n            border: 1px solid rgba(255, 255, 255, 0.1);\n        }\n        .header {\n            background: linear-gradient(135deg, #ea580c 0%, #f97316 50%, #fb923c 100%);\n            color: white;\n            padding: 50px 40px;\n            text-align: center;\n            position: relative;\n            overflow: hidden;\n        }\n        .header::before {\n            content: '';\n            position: absolute;\n            top: 0;\n            left: 0;\n            right: 0;\n            bottom: 0;\n            background: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\"><defs><pattern id=\"grid\" width=\"10\" height=\"10\" patternUnits=\"userSpaceOnUse\"><path d=\"M 10 0 L 0 0 0 10\" fill=\"none\" stroke=\"rgba(255,255,255,0.1)\" stroke-width=\"0.5\"/></pattern></defs><rect width=\"100\" height=\"100\" fill=\"url(%23grid)\"/></svg>');\n            opacity: 0.3;\n        }\n        .header h1 {\n            margin: 0;\n            font-size: 32px;\n            font-weight: 700;\n            position: relative;\n            z-index: 1;\n            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\n        }\n        .header p {\n            margin: 12px 0 0 0;\n            font-size: 18px;\n            opacity: 0.95;\n            position: relative;\n            z-index: 1;\n        }\n        .content {\n            padding: 40px;\n            background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);\n        }\n        .highlight {\n            background: linear-gradient(135deg, rgba(251, 146, 60, 0.15) 0%, rgba(249, 115, 22, 0.15) 100%);\n            padding: 24px;\n            border-radius: 12px;\n            margin: 24px 0;\n            border: 1px solid rgba(251, 146, 60, 0.3);\n            position: relative;\n        }\n        .highlight::before {\n            content: '';\n            position: absolute;\n            left: 0;\n            top: 0;\n            bottom: 0;\n            width: 4px;\n            background: linear-gradient(135deg, #ea580c 0%, #f97316 100%);\n            border-radius: 2px;\n        }\n        .feature {\n            background: rgba(15, 23, 42, 0.8);\n            padding: 24px;\n            margin: 16px 0;\n            border-radius: 12px;\n            border: 1px solid rgba(255, 255, 255, 0.1);\n            position: relative;\n        }\n        .feature::before {\n            content: '';\n            position: absolute;\n            left: 0;\n            top: 0;\n            bottom: 0;\n            width: 3px;\n            background: linear-gradient(135deg, #ea580c 0%, #f97316 100%);\n            border-radius: 2px;\n        }\n        .feature strong {\n            color: #fb923c;\n            display: block;\n            margin-bottom: 8px;\n            font-size: 16px;\n        }\n        .feature-text {\n            color: #f1f5f9 !important;\n        }\n        .button {\n            display: inline-block;\n            background: linear-gradient(135deg, #ea580c 0%, #f97316 100%);\n            color: #ffffff !important;\n            padding: 16px 32px;\n            text-decoration: none;\n            border-radius: 10px;\n            font-weight: 600;\n            margin: 12px 12px 12px 0;\n            box-shadow: 0 4px 14px 0 rgba(249, 115, 22, 0.3);\n            border: 1px solid rgba(251, 146, 60, 0.2);\n        }\n        .button-secondary {\n            background: linear-gradient(135deg, #374151 0%, #4b5563 100%);\n            box-shadow: 0 4px 14px 0 rgba(75, 85, 99, 0.3);\n            border: 1px solid rgba(156, 163, 175, 0.2);\n            color: #ffffff !important;\n        }\n        .footer {\n            background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);\n            padding: 40px;\n            text-align: center;\n            font-size: 14px;\n            color: #9ca3af;\n            border-top: 1px solid rgba(255, 255, 255, 0.1);\n        }\n        .footer strong {\n            color: #fb923c;\n        }\n        .footer a {\n            color: #fb923c;\n            text-decoration: none;\n        }\n        ul, ol {\n            padding-left: 20px;\n            color: #f1f5f9;\n        }\n        li {\n            margin-bottom: 10px;\n            color: #f1f5f9;\n        }\n        .text-center {\n            text-align: center;\n        }\n        .tier-badge {\n            display: inline-block;\n            background: linear-gradient(135deg, #059669 0%, #10b981 100%);\n            color: white;\n            padding: 6px 16px;\n            border-radius: 20px;\n            font-size: 12px;\n            font-weight: 600;\n            text-transform: uppercase;\n            letter-spacing: 0.5px;\n            box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);\n        }\n        h2 {\n            color: #fb923c;\n            margin-top: 32px;\n            margin-bottom: 16px;\n            font-size: 24px;\n            font-weight: 600;\n        }\n        p {\n            color: #f1f5f9;\n            margin-bottom: 16px;\n        }\n    </style>\n</head>\n<body>\n    <div class=\"container\">\n        <div class=\"header\">\n            <h1>Welcome to RouKey! 🚀</h1>\n            <p>Your AI Gateway is Ready</p>\n        </div>\n        \n        <div class=\"content\">\n            <p>Hi <strong>${userName}</strong>,</p>\n\n            <p>Welcome to RouKey! We're excited to have you join our community of developers building smarter AI applications with intelligent routing and cost optimization.</p>\n\n            <div class=\"highlight\">\n                <p><strong>⚡ Your <span class=\"tier-badge\">${userTier}</span> account is now active and ready to use!</strong></p>\n            </div>\n\n            <h2>🚀 Quick Start Guide</h2>\n\n            <div class=\"feature\">\n                <strong>1. Access Your Dashboard</strong>\n                <span class=\"feature-text\">Start configuring your AI routing strategies and monitoring performance in real-time.</span>\n            </div>\n\n            <div class=\"feature\">\n                <strong>2. Add Your API Keys</strong>\n                <span class=\"feature-text\">Connect your OpenAI, Anthropic, Google, and other provider keys for intelligent routing across 300+ models.</span>\n            </div>\n\n            <div class=\"feature\">\n                <strong>3. Configure Smart Routing</strong>\n                <span class=\"feature-text\">Choose from intelligent routing strategies: complexity-based, cost-optimized, role-based, and more.</span>\n            </div>\n\n            <div class=\"feature\">\n                <strong>4. Start Optimizing</strong>\n                <span class=\"feature-text\">Begin saving costs immediately with automatic model selection and fallback protection.</span>\n            </div>\n\n            <h2>✨ What You Can Do Next</h2>\n            <ul>\n                <li><strong>Intelligent Routing:</strong> Let RouKey automatically route requests to the optimal model based on complexity and cost</li>\n                <li><strong>Multiple Providers:</strong> Add keys from 50+ AI providers for maximum reliability and performance</li>\n                <li><strong>Real-time Analytics:</strong> Monitor costs, latency, success rates, and performance metrics</li>\n                <li><strong>Advanced Features:</strong> Explore custom roles, semantic caching, and knowledge base integration</li>\n            </ul>\n\n            <h2>💡 Pro Tips for Success</h2>\n            <ol>\n                <li><strong>Start Simple:</strong> Begin with fallback routing, then explore advanced strategies</li>\n                <li><strong>Diversify Providers:</strong> Add multiple API keys for better reliability and cost optimization</li>\n                <li><strong>Monitor Performance:</strong> Use our analytics dashboard to track savings and optimize further</li>\n            </ol>\n\n            <div class=\"text-center\" style=\"margin: 40px 0;\">\n                <a href=\"https://roukey.online/dashboard\" class=\"button\">Open Dashboard</a>\n                <a href=\"https://roukey.online/docs\" class=\"button button-secondary\">View Documentation</a>\n            </div>\n\n            <p>Thank you for choosing RouKey. We're here to help you build better AI applications with smarter routing!</p>\n\n            <p>Best regards,<br>\n            <strong>The RouKey Team</strong></p>\n        </div>\n        \n        <div class=\"footer\">\n            <p><strong>RouKey - Smart AI Gateway</strong></p>\n            <p>© ${new Date().getFullYear()} DRIM LLC. All rights reserved.</p>\n            <p>Need help? Reply to this email or contact us at <a href=\"mailto:<EMAIL>\" style=\"color: #ff6b35;\"><EMAIL></a></p>\n            <p style=\"margin-top: 20px; font-size: 12px; color: #999;\">\n                You received this email because you signed up for RouKey. \n                If you have any questions, please contact our support team.\n            </p>\n        </div>\n    </div>\n</body>\n</html>`;\n        // Send email using Resend\n        console.log('🔍 RESEND: Initializing Resend client...');\n        let resend;\n        try {\n            resend = getResendClient();\n        } catch (clientError) {\n            console.error('❌ RESEND: Failed to initialize Resend client:', clientError);\n            return false;\n        }\n        console.log('🔍 RESEND: Sending email...');\n        const { data: emailData, error } = await resend.emails.send({\n            from: 'RouKey <<EMAIL>>',\n            to: [\n                userEmail\n            ],\n            subject: 'Welcome to RouKey - Your AI Gateway is Ready! 🚀',\n            html: htmlContent,\n            text: `Hi ${userName},\n\nWelcome to RouKey! Your ${userTier} account is now active.\n\nQuick Start:\n1. Access Your Dashboard: https://roukey.online/dashboard\n2. Add Your API Keys\n3. Set Up Routing\n4. Start Saving\n\nNeed help? Contact <NAME_EMAIL>\n\nBest regards,\nThe RouKey Team\n\n© ${new Date().getFullYear()} DRIM LLC. All rights reserved.`\n        });\n        if (error) {\n            console.error('❌ Resend error:', error);\n            return false;\n        }\n        console.log('✅ Welcome email sent successfully via Resend to:', userEmail);\n        console.log('📧 Email ID:', emailData?.id);\n        return true;\n    } catch (error) {\n        console.error('❌ Failed to send welcome email via Resend:', error);\n        return false;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/email/resendWelcomeEmail.ts\n");

/***/ })

};
;